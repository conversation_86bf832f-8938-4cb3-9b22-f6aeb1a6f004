'use client';

import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  Di<PERSON>Header, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  Loader2, 
  Download, 
  Shield
} from 'lucide-react';

interface MarketplaceTemplate {
  id: string;
  name: string;
  description: string;
  tags: string[];
  download_count: number;
  creator_name: string;
  created_at: string;
  marketplace_published_at?: string;
  avatar?: string;
  avatar_color?: string;
  template_id: string;
  is_kortix_team?: boolean;
  mcp_requirements?: Array<{
    qualified_name: string;
    display_name: string;
    enabled_tools?: string[];
    required_config: string[];
    custom_type?: 'sse' | 'http';
  }>;
  metadata?: {
    source_agent_id?: string;
    source_version_id?: string;
    source_version_name?: string;
  };
}

interface InstallDialogComposioProps {
  item: MarketplaceTemplate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInstall: (item: MarketplaceTemplate, instanceName?: string) => Promise<void>;
  isInstalling: boolean;
}

export const InstallDialogComposio: React.FC<InstallDialogComposioProps> = ({
  item,
  open,
  onOpenChange,
  onInstall,
  isInstalling
}) => {
  const [instanceName, setInstanceName] = useState('');

  // Set default instance name when dialog opens
  useEffect(() => {
    if (item && open) {
      setInstanceName(`${item.name}`);
    }
  }, [item, open]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setInstanceName('');
    }
  }, [open]);

  const handleInstall = async () => {
    if (!item) return;

    if (!instanceName.trim()) {
      return;
    }

    await onInstall(item, instanceName);
  };

  const canInstall = () => {
    return item && instanceName.trim() && !isInstalling;
  };

  if (!item) return null;

  // Extract Composio app requirements for display
  const composioRequirements = item.mcp_requirements?.filter(
    req => req.qualified_name.startsWith('composio/')
  ) || [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader className="space-y-3">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
              <Shield className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            Install {item.name}
          </DialogTitle>
          <DialogDescription>
            Give your new agent a name to add it to your library
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Instance Name Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Agent Name</label>
            <Input
              value={instanceName}
              onChange={(e) => setInstanceName(e.target.value)}
              placeholder="Enter a name for this agent"
              className="h-11"
              autoFocus
            />
          </div>

          {/* Show connected apps (read-only) */}
          {composioRequirements.length > 0 && (
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">Connected Apps</label>
              <div className="flex flex-wrap gap-2">
                {composioRequirements.map((req) => {
                  const appName = req.display_name;
                  const toolCount = req.enabled_tools?.length || 0;
                  
                  return (
                    <Badge 
                      key={req.qualified_name} 
                      variant="outline" 
                      className="text-xs"
                    >
                      <CheckCircle className="h-3 w-3 mr-1 text-green-600" />
                      {appName} ({toolCount} tools)
                    </Badge>
                  );
                })}
              </div>
            </div>
          )}

          {/* Ready to install alert */}
          <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/50">
            <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertDescription className="text-green-800 dark:text-green-200">
              All requirements are met. Your agent is ready to install!
            </AlertDescription>
          </Alert>

          {/* Template info */}
          <div className="text-sm text-muted-foreground space-y-1">
            <p>Created by: {item.creator_name}</p>
            <p>Downloads: {item.download_count}</p>
            {item.metadata?.source_version_name && (
              <p>Version: {item.metadata.source_version_name}</p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleInstall}
            disabled={!canInstall()}
          >
            {isInstalling ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Installing...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Install Agent
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};