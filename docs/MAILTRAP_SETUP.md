# Mailtrap Welcome Email Setup

This guide shows you how to set up automated welcome emails using **only Supabase** and Mailtrap, without touching your main application code.

## Overview

The system works entirely within Supabase:
1. **Database Trigger**: Fires when new users sign up
2. **Edge Function**: Sends welcome emails via Mailtrap API
3. **Automatic**: No code changes needed in your main app

## Prerequisites

1. Mailtrap account (free tier works)
2. Supabase CLI installed
3. Access to your Supabase project dashboard

## Step 1: Get Mailtrap Credentials

1. Go to [Mailtrap.io](https://mailtrap.io) and sign up/login
2. Navigate to **Email Sending** > **Sending Domains**
3. Add and verify your domain (or use their sandbox for testing)
4. Go to **API Tokens** and create a new token
5. Note down:
   - API Token
   - Account ID (from URL or account settings)
   - Inbox ID (from your inbox settings)

## Step 2: Deploy the System

Run the deployment script:

```bash
chmod +x scripts/deploy-mailtrap-welcome.sh
./scripts/deploy-mailtrap-welcome.sh
```

## Step 3: Configure Supabase Secrets

1. Go to your [Supabase Edge Functions settings](https://supabase.com/dashboard/project/twmdhuwpsgvqbbbekzdu/settings/edge-functions)
2. Add these environment variables:

```
MAILTRAP_API_TOKEN=your_api_token_here
MAILTRAP_ACCOUNT_ID=your_account_id_here
MAILTRAP_INBOX_ID=your_inbox_id_here
MAILTRAP_FROM_EMAIL=<EMAIL>
MAILTRAP_FROM_NAME=Atlas Team
```

## Step 4: Test the Setup

1. Create a test user in your app
2. Check the [Supabase logs](https://supabase.com/dashboard/project/twmdhuwpsgvqbbbekzdu/logs/edge-functions)
3. Verify the welcome email was sent in Mailtrap

## How It Works

### Database Trigger
```sql
-- Automatically fires when new users sign up
CREATE TRIGGER on_auth_user_created_mailtrap_welcome
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.send_mailtrap_welcome_email();
```

### Edge Function
- Receives user email and name
- Formats a beautiful HTML welcome email
- Sends via Mailtrap API
- Handles errors gracefully

### Benefits

✅ **Zero code changes** to your main application
✅ **Automatic** - works for all signup methods
✅ **Reliable** - uses Supabase's robust infrastructure
✅ **Scalable** - handles high volume automatically
✅ **Maintainable** - all email logic in one place

## Email Sequences with Mailtrap

Once users are in Mailtrap, you can:

1. **Create email sequences** in Mailtrap dashboard
2. **Set up automations** based on user behavior
3. **Track engagement** and analytics
4. **A/B test** different email content

## Troubleshooting

### Check Logs
```bash
# View edge function logs
supabase functions logs mailtrap-welcome --follow
```

### Common Issues

1. **Missing credentials**: Ensure all environment variables are set
2. **Domain not verified**: Verify your sending domain in Mailtrap
3. **Rate limits**: Check Mailtrap usage limits

### Manual Testing

You can manually test the edge function:

```bash
curl -X POST 'https://twmdhuwpsgvqbbbekzdu.supabase.co/functions/v1/mailtrap-welcome' \
  -H 'Authorization: Bearer YOUR_SERVICE_ROLE_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"email": "<EMAIL>", "name": "Test User"}'
```

## Advanced Configuration

### Custom Email Templates
Edit `backend/supabase/functions/mailtrap-welcome/index.ts` to customize the email template.

### Different Email Types
Create additional edge functions for different email types (password reset, notifications, etc.).

### Integration with Marketing Tools
Use Mailtrap's API to add users to specific lists or trigger different email sequences based on user properties.
