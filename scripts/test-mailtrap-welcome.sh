#!/bin/bash

# Test script for Mailtrap welcome email function
# Usage: ./scripts/test-mailtrap-welcome.sh <EMAIL> "Test User"

set -e

EMAIL=${1:-"<EMAIL>"}
NAME=${2:-"Test User"}
SUPABASE_URL="https://twmdhuwpsgvqbbbekzdu.supabase.co"

# You need to get your service role key from Supabase dashboard
# Go to: https://supabase.com/dashboard/project/twmdhuwpsgvqbbbekzdu/settings/api
SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY:-"your_service_role_key_here"}

echo "🧪 Testing Mailtrap welcome email function..."
echo "📧 Email: $EMAIL"
echo "👤 Name: $NAME"
echo ""

if [ "$SERVICE_ROLE_KEY" = "your_service_role_key_here" ]; then
    echo "❌ Error: Please set SUPABASE_SERVICE_ROLE_KEY environment variable"
    echo "Get it from: https://supabase.com/dashboard/project/twmdhuwpsgvqbbbekzdu/settings/api"
    exit 1
fi

# Test the edge function directly
echo "📤 Calling edge function..."
response=$(curl -s -w "\n%{http_code}" -X POST \
    "$SUPABASE_URL/functions/v1/mailtrap-welcome" \
    -H "Authorization: Bearer $SERVICE_ROLE_KEY" \
    -H "Content-Type: application/json" \
    -d "{\"email\": \"$EMAIL\", \"name\": \"$NAME\"}")

# Extract response body and status code
body=$(echo "$response" | head -n -1)
status_code=$(echo "$response" | tail -n 1)

echo "📊 Response Status: $status_code"
echo "📄 Response Body: $body"

if [ "$status_code" = "200" ]; then
    echo "✅ Test successful! Welcome email should be sent."
else
    echo "❌ Test failed. Check the response above for details."
    echo ""
    echo "Common issues:"
    echo "1. Edge function not deployed: Run 'supabase functions deploy mailtrap-welcome'"
    echo "2. Missing environment variables in Supabase dashboard"
    echo "3. Invalid Mailtrap credentials"
    echo "4. Incorrect service role key"
fi
