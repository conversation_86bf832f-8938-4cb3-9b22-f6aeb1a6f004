#!/bin/bash

# Deploy Mailtrap Welcome Email System to Supabase
# This script sets up the complete Mailtrap integration using only Supabase

set -e

echo "🚀 Deploying Mailtrap Welcome Email System..."

# Check if we're in the right directory
if [ ! -f "backend/supabase/config.toml" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Change to backend directory
cd backend

echo "📦 Deploying edge function..."
supabase functions deploy mailtrap-welcome

echo "🗄️  Running database migration..."
supabase db push

echo "🔧 Setting up Supabase secrets..."
echo "Please set the following secrets in your Supabase dashboard:"
echo ""
echo "1. Go to: https://supabase.com/dashboard/project/twmdhuwpsgvqbbbekzdu/settings/edge-functions"
echo "2. Add these environment variables:"
echo ""
echo "   MAILTRAP_API_TOKEN=your_mailtrap_api_token"
echo "   MAILTRAP_ACCOUNT_ID=your_mailtrap_account_id"
echo "   MAILTRAP_INBOX_ID=your_mailtrap_inbox_id"
echo "   MAILTRAP_FROM_EMAIL=<EMAIL>"
echo "   MAILTRAP_FROM_NAME=Atlas Team"
echo ""
echo "📋 To get your Mailtrap credentials:"
echo "1. Go to https://mailtrap.io/inboxes"
echo "2. Create or select an inbox"
echo "3. Go to 'Email Sending' > 'Sending Domains'"
echo "4. Get your API token from 'API Tokens' section"
echo ""

echo "✅ Deployment complete!"
echo ""
echo "🔍 How it works:"
echo "1. When a new user signs up, a database trigger fires"
echo "2. The trigger calls the mailtrap-welcome edge function"
echo "3. The edge function sends a welcome email via Mailtrap API"
echo "4. Users are automatically added to your Mailtrap audience"
echo ""
echo "🧪 To test:"
echo "1. Create a test user in your app"
echo "2. Check Supabase logs: https://supabase.com/dashboard/project/twmdhuwpsgvqbbbekzdu/logs/edge-functions"
echo "3. Check Mailtrap inbox for the welcome email"
