-- Enable the pg_net extension for HTTP requests
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Create a function to send welcome emails via Mailtrap edge function
CREATE OR REPLACE FUNCTION public.send_mailtrap_welcome_email()
RETURNS TRIGGER AS $$
DECLARE
  user_name TEXT;
  supabase_url TEXT;
  service_role_key TEXT;
  response_status INTEGER;
BEGIN
  -- Extract name from email (simple fallback)
  user_name := split_part(NEW.email, '@', 1);
  user_name := replace(replace(replace(user_name, '.', ' '), '_', ' '), '-', ' ');
  user_name := initcap(user_name);

  -- Get Supabase URL and service role key from settings
  supabase_url := current_setting('app.settings.supabase_url', true);
  service_role_key := current_setting('app.settings.service_role_key', true);

  -- If settings are not available, use environment defaults
  IF supabase_url IS NULL THEN
    supabase_url := 'https://twmdhuwpsgvqbbbekzdu.supabase.co';
  END IF;

  -- Call the Mailtrap edge function to send welcome email
  SELECT status INTO response_status
  FROM net.http_post(
    url := supabase_url || '/functions/v1/mailtrap-welcome',
    headers := jsonb_build_object(
      'Content-Type', 'application/json',
      'Authorization', 'Bearer ' || COALESCE(service_role_key, current_setting('app.settings.service_role_key', true))
    ),
    body := jsonb_build_object(
      'email', NEW.email,
      'name', user_name
    )
  );

  -- Log the response status
  IF response_status = 200 THEN
    RAISE NOTICE 'Welcome email sent successfully for user %', NEW.email;
  ELSE
    RAISE WARNING 'Failed to send welcome email for user %. HTTP status: %', NEW.email, response_status;
  END IF;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE WARNING 'Failed to send welcome email for user %: %', NEW.email, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to send welcome email on new user signup
DROP TRIGGER IF EXISTS on_auth_user_created_mailtrap_welcome ON auth.users;
CREATE TRIGGER on_auth_user_created_mailtrap_welcome
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.send_mailtrap_welcome_email();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA net TO postgres, anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION net.http_post TO postgres, anon, authenticated, service_role;
