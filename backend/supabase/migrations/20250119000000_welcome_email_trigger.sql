-- Create a function to send welcome emails via edge function
CREATE OR REPLACE FUNCTION public.send_welcome_email_trigger()
RETURNS TRIGGER AS $$
DECLARE
  user_name TEXT;
BEGIN
  -- Extract name from email (simple fallback)
  user_name := split_part(NEW.email, '@', 1);
  user_name := replace(replace(replace(user_name, '.', ' '), '_', ' '), '-', ' ');
  user_name := initcap(user_name);

  -- Call the edge function to send welcome email
  -- Using pg_net extension to make HTTP requests
  PERFORM
    net.http_post(
      url := current_setting('app.settings.supabase_url') || '/functions/v1/send-welcome-email',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || current_setting('app.settings.service_role_key')
      ),
      body := jsonb_build_object(
        'email', NEW.email,
        'name', user_name
      )
    );

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE WARNING 'Failed to send welcome email for user %: %', NEW.email, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to send welcome email when new user is created
DROP TRIGGER IF EXISTS on_auth_user_created_send_welcome_email ON auth.users;

CREATE TRIGGER on_auth_user_created_send_welcome_email
  AFTER INSERT ON auth.users
  FOR EACH ROW
  WHEN (NEW.email IS NOT NULL AND NEW.email_confirmed_at IS NOT NULL)
  EXECUTE FUNCTION public.send_welcome_email_trigger();

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.send_welcome_email_trigger() TO service_role;
