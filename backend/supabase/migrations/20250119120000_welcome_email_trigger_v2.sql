-- Enable the pg_net extension for HTTP requests
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Create a function to send welcome emails via our new edge function
CREATE OR REPLACE FUNCTION public.send_welcome_email_on_signup()
RETURNS TRIGGER AS $$
DECLARE
  user_name TEXT;
  supabase_url TEXT;
  service_role_key TEXT;
BEGIN
  -- Extract name from email (simple fallback)
  user_name := split_part(NEW.email, '@', 1);
  user_name := replace(replace(replace(user_name, '.', ' '), '_', ' '), '-', ' ');
  user_name := initcap(user_name);

  -- Get Supabase URL and service role key from settings
  supabase_url := current_setting('app.settings.supabase_url', true);
  service_role_key := current_setting('app.settings.service_role_key', true);

  -- If settings are not available, use environment defaults
  IF supabase_url IS NULL THEN
    supabase_url := 'https://twmdhuwpsgvqbbbekzdu.supabase.co';
  END IF;

  -- Call the edge function to send welcome email
  PERFORM
    net.http_post(
      url := supabase_url || '/functions/v1/welcome-email',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || COALESCE(service_role_key, current_setting('app.settings.service_role_key', true))
      ),
      body := jsonb_build_object(
        'email', NEW.email,
        'name', user_name
      )
    );

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE WARNING 'Failed to send welcome email for user %: %', NEW.email, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to send welcome email on user signup
DROP TRIGGER IF EXISTS on_auth_user_created_welcome_email ON auth.users;
CREATE TRIGGER on_auth_user_created_welcome_email
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.send_welcome_email_on_signup();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA net TO postgres, anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION net.http_post TO postgres, anon, authenticated, service_role;
