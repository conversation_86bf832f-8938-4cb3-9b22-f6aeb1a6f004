import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Welcome email HTML template
const getWelcomeEmailHTML = (displayName: string) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Atlas</title>
</head>
<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f5f5f5;">
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; margin-top: 40px;">
        <tr>
            <td style="padding: 40px 30px; text-align: center; background-color: #f8f9fa;">
                <h1 style="color: #155dfc; font-size: 28px; margin-bottom: 16px;">Welcome to Atlas!</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 40px 30px;">
                <h2 style="color: #333; font-size: 24px; margin-bottom: 20px;">Hi ${displayName}! 👋</h2>

                <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                    Thank you for signing up for Atlas! Your AI assistant is ready to help you be more productive.
                </p>

                <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
                    Get started by visiting your dashboard and creating your first AI agent.
                </p>

                <div style="text-align: center; margin-bottom: 30px;">
                    <a href="https://www.atlasagents.ai/dashboard" style="display: inline-block; background-color: #155dfc; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: 600;">
                        Go to Dashboard
                    </a>
                </div>

                <p style="color: #999; font-size: 14px; line-height: 1.6;">
                    If you have any questions, feel free to reach out to our support team.
                </p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px 30px; text-align: center; background-color: #f8f9fa; border-top: 1px solid #eee;">
                <p style="color: #999; font-size: 12px; margin: 0;">
                    © 2025 Atlas. All rights reserved.
                </p>
            </td>
        </tr>
    </table>
</body>
</html>
`

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { email, name } = await req.json()

    if (!email) {
      throw new Error('Email is required')
    }

    // Get user display name
    const displayName = name || email.split('@')[0].replace(/[._-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())

    // Get Mailtrap configuration from environment
    const mailtrapToken = Deno.env.get('MAILTRAP_API_TOKEN')
    const mailtrapAccountId = Deno.env.get('MAILTRAP_ACCOUNT_ID')
    const mailtrapInboxId = Deno.env.get('MAILTRAP_INBOX_ID')
    const fromEmail = Deno.env.get('MAILTRAP_FROM_EMAIL') || '<EMAIL>'
    const fromName = Deno.env.get('MAILTRAP_FROM_NAME') || 'Atlas Team'

    if (!mailtrapToken || !mailtrapAccountId || !mailtrapInboxId) {
      throw new Error('Mailtrap credentials not configured')
    }

    // Create email content
    const htmlContent = getWelcomeEmailHTML(displayName)
    const textContent = `
Hi ${displayName}!

Thank you for signing up for Atlas! Your AI assistant is ready to help you be more productive.

Get started: https://www.atlasagents.ai/dashboard

If you have any questions, feel free to reach out to our support team.

© 2025 Atlas. All rights reserved.
    `.trim()

    // Send email using Mailtrap API
    const emailData = {
      from: {
        email: fromEmail,
        name: fromName
      },
      to: [
        {
          email: email,
          name: displayName
        }
      ],
      subject: 'Welcome to Atlas - Your AI Assistant is Ready!',
      html: htmlContent,
      text: textContent,
      category: 'welcome'
    }

    console.log(`Sending welcome email to ${email} (${displayName})`)

    const response = await fetch(`https://send.api.mailtrap.io/api/send`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${mailtrapToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailData)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Mailtrap API error: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    console.log('Email sent successfully:', result)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Welcome email sent successfully',
        email: email,
        name: displayName,
        messageId: result.message_ids?.[0]
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Error sending welcome email:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
