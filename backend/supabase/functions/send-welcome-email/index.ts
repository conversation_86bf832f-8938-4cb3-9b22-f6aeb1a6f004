import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { email, name } = await req.json()
    
    if (!email) {
      throw new Error('Email is required')
    }

    // Get Mailtrap credentials from environment
    const mailtrapUsername = Deno.env.get('MAILTRAP_SMTP_USERNAME')
    const mailtrapPassword = Deno.env.get('MAILTRAP_SMTP_PASSWORD')
    const fromEmail = Deno.env.get('MAILTRAP_FROM_EMAIL') || '<EMAIL>'
    const fromName = Deno.env.get('MAILTRAP_FROM_NAME') || 'Atlas Team'

    if (!mailtrapUsername || !mailtrapPassword) {
      throw new Error('Mailtrap credentials not configured')
    }

    // Call your backend API to send the email
    const backendUrl = Deno.env.get('BACKEND_URL') || 'http://host.docker.internal:8000'
    
    const response = await fetch(`${backendUrl}/api/send-welcome-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        name,
      }),
    })

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status}`)
    }

    const result = await response.json()

    return new Response(
      JSON.stringify({ success: true, message: 'Welcome email sent successfully' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Error sending welcome email:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
