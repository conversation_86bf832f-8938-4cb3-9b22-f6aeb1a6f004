import os
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional
from utils.config import config

logger = logging.getLogger(__name__)


class EmailService:
    def __init__(self):
        # Mailtrap SMTP configuration
        self.smtp_host = os.getenv("MAILTRAP_SMTP_HOST", "live.smtp.mailtrap.io")
        self.smtp_port = int(os.getenv("MAILTRAP_SMTP_PORT", "587"))
        self.smtp_username = os.getenv("MAILTRAP_SMTP_USERNAME")
        self.smtp_password = os.getenv("MAILTRAP_SMTP_PASSWORD")
        self.from_email = os.getenv("MAILTRAP_FROM_EMAIL", "<EMAIL>")
        self.from_name = os.getenv("MAILTRAP_FROM_NAME", "Atlas Team")

        if not self.smtp_username or not self.smtp_password:
            logger.warning(
                "MAILTRAP_SMTP_USERNAME or MAILTRAP_SMTP_PASSWORD not found in environment variables"
            )

    def send_welcome_email(
        self, user_email: str, user_name: Optional[str] = None
    ) -> bool:
        """Send welcome email using Mailtrap SMTP"""
        if not self.smtp_username or not self.smtp_password:
            logger.error("Cannot send email: Mailtrap SMTP credentials not configured")
            return False

        try:
            # Create message
            msg = MIMEMultipart("alternative")
            msg["Subject"] = "Welcome to Atlas - Your AI Assistant is Ready!"
            msg["From"] = f"{self.from_name} <{self.from_email}>"
            msg["To"] = user_email

            # Create HTML content
            display_name = user_name if user_name else user_email.split("@")[0]
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Welcome to Atlas</title>
            </head>
            <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f5f5f5;">
                <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; margin-top: 40px;">
                    <tr>
                        <td style="padding: 40px 30px; text-align: center; background-color: #f8f9fa;">
                            <h1 style="color: #155dfc; font-size: 28px; margin-bottom: 16px;">Welcome to Atlas!</h1>
                            <p style="font-size: 16px; color: #6b7280; margin-bottom: 32px;">
                                Hi {display_name},<br><br>
                                Thank you for signing up! Your AI assistant is ready to help you be more productive.
                            </p>
                            <div style="text-align: center; margin-bottom: 32px;">
                                <a href="https://yourdomain.com/dashboard" style="display: inline-block; background-color: #155dfc; color: white; font-weight: 600; font-size: 15px; padding: 12px 28px; border-radius: 9999px; text-decoration: none;">
                                    Get Started
                                </a>
                            </div>
                            <p style="font-size: 14px; color: #9ca3af; margin-top: 32px;">
                                If you have any questions, feel free to reach out to our support team.
                            </p>
                        </td>
                    </tr>
                </table>
            </body>
            </html>
            """

            # Create plain text version
            text_content = f"""
            Welcome to Atlas!

            Hi {display_name},

            Thank you for signing up! Your AI assistant is ready to help you be more productive.

            Get started: https://yourdomain.com/dashboard

            If you have any questions, feel free to reach out to our support team.
            """

            # Attach parts
            text_part = MIMEText(text_content, "plain")
            html_part = MIMEText(html_content, "html")

            msg.attach(text_part)
            msg.attach(html_part)

            # Send email
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)

            logger.info(f"Welcome email sent successfully to {user_email}")
            return True

        except Exception as e:
            logger.error(f"Error sending welcome email to {user_email}: {str(e)}")
            return False


email_service = EmailService()
